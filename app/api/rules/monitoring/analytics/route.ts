import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { RuleManagementService } from '@/lib/services/rule-management'
import { AnalyticsRepository } from '@/lib/repositories/analytics'
import { handleApiError, withErrorContext } from '@/lib/utils/error-logger'
import { z } from 'zod'

// Request validation schemas
const analyticsQuerySchema = z.object({
  linkId: z.string().optional(),
  conditionId: z.string().optional(),
  timeRange: z.enum(['1h', '6h', '24h', '7d', '30d', '90d']).default('7d'),
  granularity: z.enum(['minute', 'hour', 'day']).default('day'),
  metrics: z.array(z.enum([
    'rule_evaluations',
    'condition_matches',
    'fallback_usage',
    'cache_hits',
    'unique_visitors',
    'conversion_rate',
    'performance_score'
  ])).default(['rule_evaluations', 'condition_matches', 'fallback_usage']),
  segmentBy: z.array(z.enum([
    'condition_type',
    'priority_range',
    'visitor_country',
    'visitor_device',
    'time_of_day',
    'day_of_week'
  ])).optional(),
  includeComparison: z.boolean().default(false),
  includeInsights: z.boolean().default(true),
  includeRecommendations: z.boolean().default(true)
})

const usageReportSchema = z.object({
  reportType: z.enum(['summary', 'detailed', 'optimization', 'trends']).default('summary'),
  timeRange: z.enum(['7d', '30d', '90d']).default('30d'),
  includeCharts: z.boolean().default(true),
  format: z.enum(['json', 'csv', 'pdf']).default('json'),
  linkIds: z.array(z.string()).optional(),
  conditionTypes: z.array(z.enum(['referrer', 'location', 'device', 'time', 'schedule'])).optional()
})

// Error codes for consistent error handling
export const ERROR_CODES = {
  UNAUTHORIZED: 'UNAUTHORIZED',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  INSUFFICIENT_DATA: 'INSUFFICIENT_DATA',
  SERVER_ERROR: 'SERVER_ERROR'
} as const

type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES]

interface ApiError {
  error: string
  code: ErrorCode
  details?: string[]
  timestamp: string
}

/**
 * GET /api/rules/monitoring/analytics
 * Get rule usage analytics and insights
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      const error: ApiError = {
        error: 'Authentication required',
        code: ERROR_CODES.UNAUTHORIZED,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const rawParams = Object.fromEntries(searchParams.entries())
    
    // Convert string parameters to appropriate types
    const processedParams = {
      ...rawParams,
      metrics: rawParams.metrics ? rawParams.metrics.split(',') : undefined,
      segmentBy: rawParams.segmentBy ? rawParams.segmentBy.split(',') : undefined,
      includeComparison: rawParams.includeComparison === 'true',
      includeInsights: rawParams.includeInsights === 'true',
      includeRecommendations: rawParams.includeRecommendations === 'true'
    }

    const validatedParams = analyticsQuerySchema.parse(processedParams)

    // Initialize services
    const ruleService = RuleManagementService.getInstance()
    const analyticsRepo = new AnalyticsRepository()

    // Get rule usage analytics
    const analyticsData = await getRuleUsageAnalytics(
      validatedParams,
      session.user.id,
      ruleService,
      analyticsRepo
    )

    // Get segmented data if requested
    let segmentedData = null
    if (validatedParams.segmentBy && validatedParams.segmentBy.length > 0) {
      segmentedData = await getSegmentedAnalytics(
        validatedParams,
        session.user.id,
        analyticsRepo
      )
    }

    // Get comparison data if requested
    let comparisonData = null
    if (validatedParams.includeComparison) {
      comparisonData = await getComparisonAnalytics(
        validatedParams,
        session.user.id,
        analyticsRepo
      )
    }

    // Generate insights if requested
    let insights = null
    if (validatedParams.includeInsights) {
      insights = await generateAnalyticsInsights(
        analyticsData,
        segmentedData,
        comparisonData
      )
    }

    // Generate recommendations if requested
    let recommendations = null
    if (validatedParams.includeRecommendations) {
      recommendations = await generateAnalyticsRecommendations(
        analyticsData,
        insights,
        session.user.id,
        ruleService
      )
    }

    return NextResponse.json({
      analytics: {
        data: analyticsData,
        segments: segmentedData,
        comparison: comparisonData,
        insights,
        recommendations,
        summary: generateAnalyticsSummary(analyticsData, insights)
      },
      metadata: {
        timeRange: validatedParams.timeRange,
        granularity: validatedParams.granularity,
        metrics: validatedParams.metrics,
        segmentBy: validatedParams.segmentBy,
        dataPoints: analyticsData.dataPoints?.length || 0,
        lastUpdated: new Date().toISOString()
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      const validationError: ApiError = {
        error: 'Invalid query parameters',
        code: ERROR_CODES.VALIDATION_ERROR,
        details: error.errors.map(e => `${e.path.join('.')}: ${e.message}`),
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(validationError, { status: 400 })
    }

    return handleApiError(error, 'analytics retrieval', withErrorContext(request))
  }
}

/**
 * POST /api/rules/monitoring/analytics/report
 * Generate comprehensive usage reports
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      const error: ApiError = {
        error: 'Authentication required',
        code: ERROR_CODES.UNAUTHORIZED,
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(error, { status: 401 })
    }

    const body = await request.json()
    const validatedData = usageReportSchema.parse(body)

    // Initialize services
    const ruleService = RuleManagementService.getInstance()
    const analyticsRepo = new AnalyticsRepository()

    // Generate report based on type
    let reportData
    switch (validatedData.reportType) {
      case 'summary':
        reportData = await generateSummaryReport(validatedData, session.user.id, ruleService, analyticsRepo)
        break
      case 'detailed':
        reportData = await generateDetailedReport(validatedData, session.user.id, ruleService, analyticsRepo)
        break
      case 'optimization':
        reportData = await generateOptimizationReport(validatedData, session.user.id, ruleService, analyticsRepo)
        break
      case 'trends':
        reportData = await generateTrendsReport(validatedData, session.user.id, ruleService, analyticsRepo)
        break
      default:
        reportData = await generateSummaryReport(validatedData, session.user.id, ruleService, analyticsRepo)
    }

    // Format report based on requested format
    let formattedReport
    switch (validatedData.format) {
      case 'csv':
        formattedReport = formatReportAsCSV(reportData)
        break
      case 'pdf':
        formattedReport = await formatReportAsPDF(reportData)
        break
      default:
        formattedReport = reportData
    }

    return NextResponse.json({
      report: {
        id: generateReportId(),
        type: validatedData.reportType,
        format: validatedData.format,
        data: formattedReport,
        metadata: {
          generatedAt: new Date().toISOString(),
          timeRange: validatedData.timeRange,
          userId: session.user.id,
          includeCharts: validatedData.includeCharts
        }
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      const validationError: ApiError = {
        error: 'Invalid report configuration',
        code: ERROR_CODES.VALIDATION_ERROR,
        details: error.errors.map(e => `${e.path.join('.')}: ${e.message}`),
        timestamp: new Date().toISOString()
      }
      return NextResponse.json(validationError, { status: 400 })
    }

    return handleApiError(error, 'report generation', withErrorContext(request))
  }
}

// Helper functions
async function getRuleUsageAnalytics(params: any, userId: string, ruleService: any, analyticsRepo: any) {
  const analytics = {
    ruleEvaluations: await analyticsRepo.getRuleEvaluationStats(userId, {
      timeRange: params.timeRange,
      granularity: params.granularity,
      linkId: params.linkId,
      conditionId: params.conditionId
    }),
    conditionMatches: await analyticsRepo.getConditionMatchStats(userId, {
      timeRange: params.timeRange,
      granularity: params.granularity,
      linkId: params.linkId,
      conditionId: params.conditionId
    }),
    fallbackUsage: await analyticsRepo.getFallbackUsageStats(userId, {
      timeRange: params.timeRange,
      granularity: params.granularity,
      linkId: params.linkId
    }),
    cachePerformance: await analyticsRepo.getCachePerformanceStats(userId, {
      timeRange: params.timeRange,
      granularity: params.granularity,
      linkId: params.linkId
    }),
    visitorMetrics: await analyticsRepo.getVisitorMetrics(userId, {
      timeRange: params.timeRange,
      granularity: params.granularity,
      linkId: params.linkId
    })
  }

  return analytics
}

async function getSegmentedAnalytics(params: any, userId: string, analyticsRepo: any) {
  const segments: Record<string, any> = {}

  for (const segment of params.segmentBy) {
    switch (segment) {
      case 'condition_type':
        segments.conditionType = await analyticsRepo.getAnalyticsByConditionType(userId, {
          timeRange: params.timeRange,
          metrics: params.metrics
        })
        break
      case 'priority_range':
        segments.priorityRange = await analyticsRepo.getAnalyticsByPriorityRange(userId, {
          timeRange: params.timeRange,
          metrics: params.metrics
        })
        break
      case 'visitor_country':
        segments.visitorCountry = await analyticsRepo.getAnalyticsByCountry(userId, {
          timeRange: params.timeRange,
          metrics: params.metrics
        })
        break
      case 'visitor_device':
        segments.visitorDevice = await analyticsRepo.getAnalyticsByDevice(userId, {
          timeRange: params.timeRange,
          metrics: params.metrics
        })
        break
      case 'time_of_day':
        segments.timeOfDay = await analyticsRepo.getAnalyticsByTimeOfDay(userId, {
          timeRange: params.timeRange,
          metrics: params.metrics
        })
        break
      case 'day_of_week':
        segments.dayOfWeek = await analyticsRepo.getAnalyticsByDayOfWeek(userId, {
          timeRange: params.timeRange,
          metrics: params.metrics
        })
        break
    }
  }

  return segments
}

async function getComparisonAnalytics(params: any, userId: string, analyticsRepo: any) {
  const previousPeriod = getPreviousPeriod(params.timeRange)
  
  const comparisonData = await getRuleUsageAnalytics(
    { ...params, timeRange: previousPeriod },
    userId,
    null,
    analyticsRepo
  )

  return {
    period: 'previous_period',
    data: comparisonData,
    changes: calculateAnalyticsChanges(params.metrics, comparisonData)
  }
}

async function generateAnalyticsInsights(analyticsData: any, segmentedData: any, comparisonData: any) {
  const insights = []

  // Rule evaluation insights
  if (analyticsData.ruleEvaluations) {
    const totalEvaluations = analyticsData.ruleEvaluations.total
    const avgEvaluationsPerDay = totalEvaluations / 7 // Assuming 7-day period
    
    if (avgEvaluationsPerDay > 1000) {
      insights.push({
        type: 'high_usage',
        message: 'High rule evaluation volume detected',
        impact: 'performance',
        recommendation: 'Consider caching optimization'
      })
    }
  }

  // Fallback usage insights
  if (analyticsData.fallbackUsage) {
    const fallbackRate = analyticsData.fallbackUsage.rate
    
    if (fallbackRate > 0.1) {
      insights.push({
        type: 'high_fallback',
        message: 'High fallback usage indicates rule configuration issues',
        impact: 'user_experience',
        recommendation: 'Review rule conditions and priorities'
      })
    }
  }

  // Cache performance insights
  if (analyticsData.cachePerformance) {
    const hitRate = analyticsData.cachePerformance.hitRate
    
    if (hitRate < 0.7) {
      insights.push({
        type: 'low_cache_hit',
        message: 'Low cache hit rate affecting performance',
        impact: 'performance',
        recommendation: 'Optimize cache configuration and TTL settings'
      })
    }
  }

  // Segmented insights
  if (segmentedData?.conditionType) {
    const typePerformance = Object.entries(segmentedData.conditionType)
      .sort(([,a], [,b]) => (b as any).evaluationTime - (a as any).evaluationTime)
    
    const slowestType = typePerformance[0]
    if (slowestType && (slowestType[1] as any).evaluationTime > 50) {
      insights.push({
        type: 'slow_condition_type',
        message: `${slowestType[0]} conditions are performing slowly`,
        impact: 'performance',
        recommendation: `Optimize ${slowestType[0]} condition logic`
      })
    }
  }

  return insights
}

async function generateAnalyticsRecommendations(analyticsData: any, insights: any, userId: string, ruleService: any) {
  const recommendations = []

  // Performance recommendations
  const performanceInsights = insights?.filter((i: any) => i.impact === 'performance') || []
  if (performanceInsights.length > 0) {
    recommendations.push({
      category: 'performance',
      priority: 'high',
      title: 'Performance Optimization',
      description: 'Multiple performance issues detected',
      actions: performanceInsights.map((i: any) => i.recommendation)
    })
  }

  // Usage optimization recommendations
  if (analyticsData.conditionMatches?.matchRate < 0.5) {
    recommendations.push({
      category: 'optimization',
      priority: 'medium',
      title: 'Rule Effectiveness',
      description: 'Low condition match rate suggests rules may need refinement',
      actions: [
        'Review condition criteria',
        'Analyze visitor patterns',
        'Consider rule consolidation'
      ]
    })
  }

  // Configuration recommendations
  if (analyticsData.fallbackUsage?.rate > 0.05) {
    recommendations.push({
      category: 'configuration',
      priority: 'medium',
      title: 'Rule Configuration',
      description: 'High fallback usage indicates configuration issues',
      actions: [
        'Review rule priorities',
        'Check for conflicting conditions',
        'Validate rule logic'
      ]
    })
  }

  return recommendations
}

function generateAnalyticsSummary(analyticsData: any, insights: any) {
  return {
    totalEvaluations: analyticsData.ruleEvaluations?.total || 0,
    averageEvaluationTime: analyticsData.ruleEvaluations?.avgTime || 0,
    conditionMatchRate: analyticsData.conditionMatches?.matchRate || 0,
    fallbackRate: analyticsData.fallbackUsage?.rate || 0,
    cacheHitRate: analyticsData.cachePerformance?.hitRate || 0,
    uniqueVisitors: analyticsData.visitorMetrics?.unique || 0,
    insightsCount: insights?.length || 0,
    healthScore: calculateAnalyticsHealthScore(analyticsData),
    status: getAnalyticsHealthStatus(analyticsData)
  }
}

async function generateSummaryReport(params: any, userId: string, ruleService: any, analyticsRepo: any) {
  return {
    overview: await getRuleUsageAnalytics(params, userId, ruleService, analyticsRepo),
    topPerformingRules: await analyticsRepo.getTopPerformingRules(userId, { timeRange: params.timeRange, limit: 10 }),
    bottomPerformingRules: await analyticsRepo.getBottomPerformingRules(userId, { timeRange: params.timeRange, limit: 10 }),
    keyMetrics: await analyticsRepo.getKeyMetrics(userId, { timeRange: params.timeRange })
  }
}

async function generateDetailedReport(params: any, userId: string, ruleService: any, analyticsRepo: any) {
  return {
    ...await generateSummaryReport(params, userId, ruleService, analyticsRepo),
    detailedBreakdown: await analyticsRepo.getDetailedBreakdown(userId, { timeRange: params.timeRange }),
    performanceAnalysis: await analyticsRepo.getPerformanceAnalysis(userId, { timeRange: params.timeRange }),
    usagePatterns: await analyticsRepo.getUsagePatterns(userId, { timeRange: params.timeRange })
  }
}

async function generateOptimizationReport(params: any, userId: string, ruleService: any, analyticsRepo: any) {
  return {
    optimizationOpportunities: await ruleService.getOptimizationOpportunities(userId),
    performanceBottlenecks: await analyticsRepo.getPerformanceBottlenecks(userId, { timeRange: params.timeRange }),
    unusedRules: await analyticsRepo.getUnusedRules(userId, { timeRange: params.timeRange }),
    conflictAnalysis: await ruleService.getConflictAnalysis(userId),
    recommendations: await ruleService.getOptimizationRecommendations(userId)
  }
}

async function generateTrendsReport(params: any, userId: string, ruleService: any, analyticsRepo: any) {
  return {
    usageTrends: await analyticsRepo.getUsageTrends(userId, { timeRange: params.timeRange }),
    performanceTrends: await analyticsRepo.getPerformanceTrends(userId, { timeRange: params.timeRange }),
    errorTrends: await analyticsRepo.getErrorTrends(userId, { timeRange: params.timeRange }),
    seasonalPatterns: await analyticsRepo.getSeasonalPatterns(userId, { timeRange: params.timeRange }),
    forecasts: await analyticsRepo.getUsageForecasts(userId, { timeRange: params.timeRange })
  }
}

function formatReportAsCSV(reportData: any): string {
  // Simple CSV formatting - in practice, this would be more sophisticated
  return JSON.stringify(reportData)
}

async function formatReportAsPDF(reportData: any): Promise<string> {
  // PDF generation would be implemented here
  return 'PDF generation not implemented'
}

function getPreviousPeriod(timeRange: string): string {
  // Calculate previous period based on time range
  return timeRange
}

function calculateAnalyticsChanges(metrics: string[], comparisonData: any) {
  const changes: Record<string, any> = {}
  
  for (const metric of metrics) {
    changes[metric] = {
      absolute: 0,
      percentage: 0,
      trend: 'stable'
    }
  }
  
  return changes
}

function calculateAnalyticsHealthScore(analyticsData: any): number {
  let score = 100
  
  // Penalize high fallback rates
  if (analyticsData.fallbackUsage?.rate > 0.1) score -= 20
  else if (analyticsData.fallbackUsage?.rate > 0.05) score -= 10
  
  // Penalize low cache hit rates
  if (analyticsData.cachePerformance?.hitRate < 0.7) score -= 15
  else if (analyticsData.cachePerformance?.hitRate < 0.8) score -= 5
  
  // Penalize slow evaluation times
  if (analyticsData.ruleEvaluations?.avgTime > 100) score -= 15
  else if (analyticsData.ruleEvaluations?.avgTime > 50) score -= 5
  
  return Math.max(0, score)
}

function getAnalyticsHealthStatus(analyticsData: any): string {
  const score = calculateAnalyticsHealthScore(analyticsData)
  
  if (score >= 90) return 'excellent'
  if (score >= 75) return 'good'
  if (score >= 60) return 'fair'
  if (score >= 40) return 'poor'
  return 'critical'
}

function generateReportId(): string {
  return `report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}
